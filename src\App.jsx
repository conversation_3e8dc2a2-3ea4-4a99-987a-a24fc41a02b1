import { useContext } from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import { globalContext } from "./context/global-context";
import GlobalSigninPage from "./pages/global-signin-page";
import GlobalLayout from "./components/layout/global-layout";
import ClientLayout from "./components/layout/client-layout";
import ClientDashboardPage from "./pages/client-dashboard-page";
import InternalLayout from "./components/layout/internal-layout";
import InternalUsersPage from "./pages/internal-users-page";
import InternalMapPage from "./pages/internal-map-page";
import InternalWorkspacePage from "./pages/internal-workspace-page";
import InternalWorkforcesPage from "./pages/internal-workforces-page";

function App() {
  const { loged } = useContext(globalContext);
  return (
    <Routes>
      <Route>
        <Route path="*" element={<Navigate to={"/"} />} />
        {loged ? (
          <Route>
            <Route element={<GlobalLayout />}>
              <Route element={<ClientLayout />}>
                <Route path="" element={<ClientDashboardPage />} />
              </Route>
              <Route path="internal" element={<InternalLayout />}>
                <Route path="" element={"Internal"} />
                <Route path="map" element={<InternalMapPage />} />
                <Route path="users" element={<InternalUsersPage />} />
                <Route path="entities" element={"Entities"} />
                <Route path="sites" element={"Sites"} />
                <Route path="activities" element={"Activities"} />
                <Route path="issues" element={"Issues"} />
                <Route path="missions" element={"Missions"} />
                <Route path="workfoces" element={<InternalWorkforcesPage />} />
                <Route path="ressources" element={"Ressources"} />
                <Route path="planning" element={"Planning"} />
              </Route>
            </Route>
          </Route>
        ) : (
          <Route>
            <Route path="" element={"Public"} />
            <Route path="signin" element={<GlobalSigninPage />} />
            <Route path="signup" element={"Signup"} />
          </Route>
        )}
      </Route>
    </Routes>
  );
}

export default App;

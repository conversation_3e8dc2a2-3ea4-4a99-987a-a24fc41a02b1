import {
  Button,
  Classes,
  Collapse,
  Colors,
  Dialog,
  DialogBody,
  DialogFooter,
  Divider,
  EntityTitle,
  FormGroup,
  H6,
  InputGroup,
  MenuItem,
  Tag,
  TagInput,
  TextArea,
} from "@blueprintjs/core";
import { MultiSelect } from "@blueprintjs/select";
import { auth } from "../../services/firebase";
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { functions } from "../../services/functions";

export default function InternalFormWorkspaceNew({
  theme,
  sites,
  entities,
  ressources,
  workfoces,
  users,
}) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [name, setName] = useState("");
  const [selectedMembers, setSelectedMembers] = useState([]);
  return (
    <>
      <Dialog
        icon="folder-new"
        style={{ width: 400 }}
        className={theme === "light" ? Classes.LIGHT : Classes.DARK}
        isOpen={searchParams.get("newWorkspace") === "true"}
        title="New Workspace"
        canOutsideClickClose={false}
        onClose={() => setSearchParams()}
      >
        <form
          onSubmit={(event) => {
            event.preventDefault();
            functions.data.workspaces
              .create(event, { selectedMembers })
              .then(() => {
                setSearchParams();
              });
          }}
        >
          <DialogBody>
            {name !== "" && (
              <div style={{ display: "flex", gap: 15, marginBottom: 15 }}>
                <img
                  style={{
                    border: `1px solid ${
                      theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
                    }`,
                    borderRadius: 2,
                  }}
                  height={50}
                  src={`https://api.dicebear.com/9.x/identicon/svg?seed=${name}&radius=5&rowColor=af007c&backgroundColor=ffffff`}
                />
                <EntityTitle title={name} subtitle={"Workspace"} />
              </div>
            )}
            <FormGroup label="Name" subLabel="Name of the workspace">
              <InputGroup
                name="name"
                required
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </FormGroup>
            <FormGroup
              label="Description"
              subLabel="Description of the workspace"
            >
              <TextArea fill name="description" />
            </FormGroup>
            <FormGroup label="Members" subLabel="Add members to the workspace">
              <MultiSelect
                disabled={
                  users.filter(
                    (f) =>
                      f.type === "internal" && f.id !== auth.currentUser.uid
                  ).length === 0
                }
                items={users.filter(
                  (f) => f.type === "internal" && f.id !== auth.currentUser.uid
                )}
                itemRenderer={(item) => (
                  <MenuItem
                    key={item.id}
                    text={item.email}
                    shouldDismissPopover={false}
                    roleStructure="listitem"
                    active={selectedMembers.find((m) => m === item)}
                    onClick={() => {
                      selectedMembers.find((m) => m.id === item.id)
                        ? setSelectedMembers(
                            selectedMembers.filter((m) => m.id !== item.id)
                          )
                        : setSelectedMembers([...selectedMembers, item]);
                    }}
                  />
                )}
                onItemSelect={(item) =>
                  setSelectedMembers([...selectedMembers, item])
                }
                selectedItems={selectedMembers}
                tagRenderer={(item) => item.email}
                fill
                tagInputProps={{
                  tagProps: {
                    minimal: true,
                    onRemove: (tag, i) => {
                      console.log(i);
                      setSelectedMembers(
                        selectedMembers.filter((m) => m.email !== i.children)
                      );
                    },
                  },
                }}
                resetOnQuery
                resetOnSelect
              />
            </FormGroup>
          </DialogBody>
          <DialogFooter
            actions={[
              <Button
                text="Cancel"
                onClick={() => setSearchParams()}
                key="cancel"
              />,
              <Button
                text="Create"
                intent="primary"
                type="submit"
                key="create"
              />,
            ]}
          />
        </form>
      </Dialog>
    </>
  );
}

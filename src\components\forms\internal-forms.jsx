import InternalFormCustomerNew from "./internal-form-customer-new";
import InternalFormWorkforceNew from "./internal-form-workforce-new";
import InternalFormWorkspaceNew from "./internal-form-workspace-new";

export default function InternalForms({
  theme,
  sites,
  entities,
  ressources,
  workfoces,
  users,
}) {
  return (
    <>
      <InternalFormWorkforceNew theme={theme} users={users} />
      <InternalFormCustomerNew theme={theme} />
      <InternalFormWorkspaceNew
        theme={theme}
        sites={sites}
        entities={entities}
        ressources={ressources}
        workfoces={workfoces}
        users={users}
      />
    </>
  );
}

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>uI<PERSON>,
} from "@blueprintjs/core";
import {
  Outlet,
  useNavigate,
  useOutletContext,
  useSearchParams,
} from "react-router-dom";
import { functions } from "../../services/functions";
import Unauthorized from "../elements/unauthorized";
import DataUpload from "../elements/dataUpload";
import InternalForms from "../forms/internal-forms";
import InternalView from "../view/internal-view";
import { arrayRemove, arrayUnion, doc, updateDoc } from "firebase/firestore";
import { auth, db } from "../../services/firebase";

export default function InternalLayout() {
  const { theme, sidebarOpen, user } = useOutletContext();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  if (user.type !== "internal") return <Unauthorized />;

  const sites = functions.data.sites.InternalList();
  const fireUsers = functions.data.users.internal.ListAllFromFire();
  const users = functions.data.users.internal.listAll();
  const locations = functions.data.locations.InternalList();
  const workspaces = functions.data.workspaces.InternalList();
  const workforces = functions.data.workforces.InternalList();

  if (!users || !sites || !locations) return <DataUpload theme={theme} />;
  return (
    <>
      <InternalForms
        theme={theme}
        sites={sites}
        users={fireUsers}
        entities={[]}
        ressources={[]}
        workfoces={[]}
      />
      <InternalView theme={theme} users={users} fireUsers={fireUsers} />
      <div
        style={{
          width: sidebarOpen ? 230 : 46,
          borderRight:
            theme === "light"
              ? `1px solid ${Colors.LIGHT_GRAY1}`
              : `1px solid ${Colors.DARK_GRAY5}`,
          paddingBlock: 10,
          overflow: "hidden",
        }}
      >
        <Menu
          style={{ padding: 0, backgroundColor: "transparent" }}
          className="sidebarMenu"
        >
          <MenuItem
            title="Dashboard"
            text={sidebarOpen && "Dashboard"}
            icon="dashboard"
            intent={
              window.location.pathname === "/internal" ? "primary" : "none"
            }
            onClick={() => navigate("")}
          />
          <MenuItem
            title="Map View"
            text={sidebarOpen && "Map View"}
            icon="map"
            intent={
              window.location.pathname === "/internal/map" ? "primary" : "none"
            }
            onClick={() => navigate("map")}
          />
          <MenuItem
            title="Users"
            text={sidebarOpen && "Users"}
            icon="team"
            intent={
              window.location.pathname === "/internal/users"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("users")}
            label={users.length}
          />
          <MenuDivider />
          <MenuItem
            title="Entities"
            text={sidebarOpen && "Entities"}
            icon="office"
            intent={
              window.location.pathname === "/internal/entities"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("entities")}
            label={locations.length}
          />
          <MenuItem
            title="Sites"
            text={sidebarOpen && "Sites"}
            icon="map-marker"
            label={sites.length}
            intent={
              window.location.pathname === "/internal/sites"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("sites")}
          />
          <MenuItem
            title="Activities"
            text={sidebarOpen && "Activities"}
            icon="briefcase"
            intent={
              window.location.pathname === "/internal/activities"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("activities")}
          />
          <MenuItem
            title="Issues"
            text={sidebarOpen && "Issues"}
            icon="issue"
            intent={
              window.location.pathname === "/internal/issues"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("issues")}
          />
          <MenuDivider />
          <MenuItem
            title="Missions"
            text={sidebarOpen && "Missions"}
            icon="rocket-slant"
            intent={
              window.location.pathname === "/internal/missions"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("missions")}
          />
          <MenuItem
            title="Workfoces"
            text={sidebarOpen && "Workfoces"}
            label={workforces.length}
            icon="people"
            intent={
              window.location.pathname === "/internal/workfoces"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("workfoces")}
          />
          <MenuItem
            title="Ressources"
            text={sidebarOpen && "Ressources"}
            icon="buggy"
            intent={
              window.location.pathname === "/internal/ressources"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("ressources")}
          />
          <MenuItem
            title="Planning"
            text={sidebarOpen && "Planning"}
            icon="calendar"
            intent={
              window.location.pathname === "/internal/planning"
                ? "primary"
                : "none"
            }
            onClick={() => navigate("planning")}
          />
        </Menu>
      </div>
      <div
        style={{
          flex: 1,
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Outlet
          context={{
            theme,
            sidebarOpen,
            sites,
            users,
            fireUsers,
            locations,
            workspaces,
            workforces,
          }}
        />
      </div>
    </>
  );
}

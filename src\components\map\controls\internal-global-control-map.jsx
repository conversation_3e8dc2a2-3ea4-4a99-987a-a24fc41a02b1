import { Button, ButtonGroup } from "@blueprintjs/core";

export default function InternalGlobalControlMap({ theme, mapRef, center }) {
  return (
    <>
      <div
        style={{
          position: "absolute",
          top: 20,
          right: 20,
          display: "flex",
          gap: 15,
          flexFlow: "column",
        }}
      >
        <Button
          icon="compass"
          onClick={() =>
            mapRef.current?.flyTo({
              center: [3, 47],
              zoom: 5.5,
              bearing: 0,
              pitch: 0,
            })
          }
        />
        <ButtonGroup vertical>
          <Button icon="plus" onClick={() => mapRef.current?.zoomIn()} />
          <Button icon="minus" onClick={() => mapRef.current?.zoomOut()} />
        </ButtonGroup>
      </div>
    </>
  );
}

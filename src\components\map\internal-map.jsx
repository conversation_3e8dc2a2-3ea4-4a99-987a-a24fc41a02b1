import { useEffect, useState } from "react";
import Map from "react-map-gl/mapbox";
import InternalSearchControlMap from "./controls/internal-search-control-map";
import InternalGlobalControlMap from "./controls/internal-global-control-map";
import InternalWorkforcesCluster from "./layers/internal-workforces-cluster";
import { useOutletContext } from "react-router-dom";

export default function InternalMap({ mapRef, theme, setCenter, sidebarOpen }) {
  const { workforces } = useOutletContext();
  const [mapStyle, setMapStyle] = useState(
    theme === "light"
      ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
      : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
  );

  useEffect(() => {
    setMapStyle(
      theme === "light"
        ? "mapbox://styles/tsinovec/cmdrg8u78001d01sd5evibo4x"
        : "mapbox://styles/tsinovec/cmdrfkyi5008801r5ckbg9zvf"
    );
  }, [theme]);

  useEffect(() => {
    mapRef.current?.resize();
  }, [sidebarOpen]);

  return (
    <>
      <Map
        attributionControl={false}
        onLoad={(target) => {
          mapRef.current = target.target;
          setCenter(target.target.getCenter());
        }}
        onMove={(target) => setCenter(target.target.getCenter())}
        ref={mapRef}
        mapStyle={mapStyle}
        mapboxAccessToken={import.meta.env.VITE_MAPBOX_TOKEN}
      >
        <InternalWorkforcesCluster workforces={workforces} />
        <InternalGlobalControlMap theme={theme} mapRef={mapRef} />
        <InternalSearchControlMap theme={theme} mapRef={mapRef} />
      </Map>
    </>
  );
}

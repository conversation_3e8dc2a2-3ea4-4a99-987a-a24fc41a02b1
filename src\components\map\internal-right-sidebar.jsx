import { Colors, EntityTitle } from "@blueprintjs/core";

export default function InternalRightSidebar({ mapRef, theme, center }) {
  return (
    <>
      <div style={{ display: "flex", flexFlow: "column", flex: 1 }}>
        <div style={{ flex: 1 }}></div>
        <div
          style={{
            paddingInline: 20,
            paddingBlock: 10,
            backgroundColor:
              theme === "light" ? Colors.LIGHT_GRAY4 : Colors.DARK_GRAY2,
            borderTop: `1px solid ${
              theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
            }`,
            display: "flex",
            gap: 15,
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <EntityTitle
            title={mapRef.current?.getCenter().lat.toPrecision(6)}
            subtitle="Latitude"
          />
          <EntityTitle
            title={mapRef.current?.getCenter().lng.toPrecision(6)}
            subtitle="Longitude"
          />
          <EntityTitle
            title={mapRef.current?.getZoom().toPrecision(3)}
            subtitle={"Zoom"}
          />
        </div>
      </div>
    </>
  );
}

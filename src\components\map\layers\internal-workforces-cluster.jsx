import { Layer, Source } from "react-map-gl/mapbox";

export default function InternalWorkforcesCluster({ workforces }) {
  return (
    <>
      <Source
        id="workforces"
        type="geojson"
        data={{
          type: "FeatureCollection",
          features: workforces.map((w) => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [
                w.location.geometry.coordinates[0],
                w.location.geometry.coordinates[1],
              ],
            },
            properties: {
              id: w.id,
              job: w.job,
              city: w.location.properties.context.place.name,
              country: w.location.properties.context.country.name,
              mobility: w.mobility,
            },
          })),
        }}
      >
        <Layer
          id="workforces"
          type="circle"
          paint={{
            "circle-color": "blue",
            "circle-radius": 5,
          }}
        />
      </Source>
    </>
  );
}

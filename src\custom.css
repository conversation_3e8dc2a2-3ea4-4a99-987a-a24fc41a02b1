body ::selection {
  background: #af007c;
  color: white;
}

.sidebarMenu .bp6-menu-item {
  padding-inline: 15px;
  height: 35px;
  display: flex;
  align-items: center;
  font-weight: 500;
  gap: 5px;
}

.sidebarMenu .bp6-intent-primary {
  color: #af007c !important;
}

.sidebarMenu .bp6-intent-primary:hover {
  background-color: #af007c10 !important;
}

.sidebarMenu .bp6-intent-primary:active {
  background-color: #af007c20 !important;
}

.searchBoxInput-light input {
  color: black !important;
}

.searchBoxInput-dark input {
  color: white !important;
  box-shadow: 0 0 0 0 rgba(138, 187, 255, 0), 0 0 0 0 rgba(138, 187, 255, 0),
    inset 0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 -1px 1px 0 rgba(255, 255, 255, 0.3);
}

.bp6-tab[aria-selected="true"] {
}

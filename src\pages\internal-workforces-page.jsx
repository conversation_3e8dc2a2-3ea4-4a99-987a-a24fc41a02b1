import {
  Button,
  Colors,
  HTMLSelect,
  HTMLTable,
  InputGroup,
} from "@blueprintjs/core";
import PageTitle from "../components/elements/page-title";
import { useOutletContext, useSearchParams } from "react-router-dom";
import sortBy from "sort-by";
import { useState } from "react";
import { CSVLink } from "react-csv";
import dayjs from "dayjs";

export default function InternalWorkforcesPage() {
  const { workforces, theme } = useOutletContext();
  const [searchParams, setSearchParams] = useSearchParams();
  const [search, setSearch] = useState("");
  return (
    <>
      <PageTitle
        title="Workforces"
        actions={
          <>
            <CSVLink
              filename={`workforces-${dayjs().format(
                "DD-MM-YYYY HH:mm:ss"
              )}.csv`}
              data={workforces.map((w) => ({
                "first name": w.firstName,
                "last name": w.lastName,
                job: w.job,
                city: w.location.properties.context.place.name,
                country: w.location.properties.context.country.name,
                mobility: w.mobility,
                "activity sector": w.activitySector,
                manager: w.manager,
                created: dayjs(w.createdAt.seconds * 1000).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
                "last updated": dayjs(w.updatedAt.seconds * 1000).format(
                  "DD/MM/YYYY HH:mm:ss"
                ),
              }))}
            >
              <Button text="Export" icon="export" />
            </CSVLink>
            <Button
              text="New Workforce"
              intent="primary"
              icon="add"
              onClick={() => setSearchParams({ newWorkforce: "true" })}
            />
          </>
        }
      />
      <div
        style={{
          paddingInline: "40px 20px",
          paddingBlock: "0px 30px",
          display: "flex",
          gap: 15,
        }}
      >
        <InputGroup
          placeholder="Search"
          leftIcon="search"
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>
      <div style={{ flex: 1, paddingInline: "40px 20px" }}>
        <HTMLTable
          width={"100%"}
          interactive
          striped
          bordered
          style={{
            border: `1px solid ${
              theme === "light" ? Colors.LIGHT_GRAY1 : Colors.DARK_GRAY5
            }`,
          }}
        >
          <thead>
            <tr>
              <th
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (searchParams.get("sort") !== "firstName") {
                    setSearchParams({ sort: "firstName" });
                  } else {
                    setSearchParams({ sort: "-firstName" });
                  }
                }}
              >
                First Name
              </th>
              <th
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (searchParams.get("sort") !== "lastName") {
                    setSearchParams({ sort: "lastName" });
                  } else {
                    setSearchParams({ sort: "-lastName" });
                  }
                }}
              >
                Last Name
              </th>
              <th
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (searchParams.get("sort") !== "job") {
                    setSearchParams({ sort: "job" });
                  } else {
                    setSearchParams({ sort: "-job" });
                  }
                }}
              >
                Job
              </th>
              <th
                style={{ cursor: "pointer" }}
                onClick={() => {
                  if (
                    searchParams.get("sort") !==
                    "location.properties.context.place.name"
                  ) {
                    setSearchParams({
                      sort: "location.properties.context.place.name",
                    });
                  } else {
                    setSearchParams({
                      sort: "-location.properties.context.place.name",
                    });
                  }
                }}
              >
                City
              </th>
              <th>Country</th>
              <th>Mobility</th>
              <th>Activity Sector</th>
            </tr>
          </thead>
          <tbody>
            {workforces
              .filter((w) => {
                return (
                  w.firstName?.includes(search) ||
                  w.lastName?.includes(search) ||
                  w.job?.includes(search) ||
                  w.location.properties.context.place.name?.includes(search) ||
                  w.location.properties.context.country.name?.includes(
                    search
                  ) ||
                  w.mobility?.includes(search) ||
                  w.activitySector?.includes(search)
                );
              })
              .sort(sortBy(searchParams.get("sort")))
              .map((w) => (
                <tr key={w.id}>
                  <td>{w.firstName}</td>
                  <td>{w.lastName}</td>
                  <td>{w.job}</td>
                  <td>{w.location.properties.context.place.name}</td>
                  <td>{w.location.properties.context.country.name}</td>
                  <td>{w.mobility}</td>
                  <td>{w.activitySector}</td>
                </tr>
              ))}
          </tbody>
        </HTMLTable>
      </div>
    </>
  );
}
